"""
SVG转PPT相关路由处理模块
"""
import logging
import json
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException, WebSocket, WebSocketDisconnect, BackgroundTasks
from fastapi.responses import Response
from pydantic import BaseModel
import io
import time
import asyncio
from datetime import datetime, timedelta

# 导入SVG转PPT服务
from backend.tools.svg_to_ppt_service import SVGToPPTService
# 导入SVG生成器相关类和枚举
from backend.utils.svg_generator import GenerationStage, DesignSpecification
# 导入WebSocket管理器
from backend.api.websocket import manager

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建路由
router = APIRouter()

# 用于跟踪活跃的SVG生成会话
active_svg_sessions = {}

# 任务清理配置
TASK_CLEANUP_INTERVAL = 300   # 5分钟清理一次
TASK_KEEP_DURATION_ACTIVE = 3600    # 活跃任务保持1小时（generating状态）
TASK_KEEP_DURATION_COMPLETED = 1800 # 完成任务保持30分钟（completed/failed状态）

class ImageData(BaseModel):
    """图片数据模型"""
    imageData: str  # base64编码的图片数据
    format: str = "png"  # 图片格式，默认为png

class SVGToPPTRequest(BaseModel):
    """SVG转PPT请求模型"""
    images: List[ImageData]  # 图片数据列表
    title: str = "SVG演示文稿"  # PPT标题
    author: str = "SVG转PPT工具"  # 作者信息

class SVGGenerationRequest(BaseModel):
    """SVG生成请求模型"""
    content: str
    style_config: Optional[Dict[str, str]] = None
    custom_requirements: str = ""
    session_id: str = ""
    template_set_name: Optional[str] = None  # 新增：选择的模板集名称
    color_theme: Optional[str] = None  # 新增：选择的颜色主题  # 用于WebSocket进度通信

class SingleSVGRegenerationRequest(BaseModel):
    """单张SVG重新生成请求模型"""
    content: str
    page_index: int  # 要重新生成的页面索引（0-based）
    page_title: str = ""  # 页面标题
    style_config: Optional[Dict[str, str]] = None
    custom_requirements: str = ""
    session_id: str = ""
    original_session_id: str = ""  # 原始生成的会话ID，用于获取设计规范

# 新增：SVG生成状态数据模型
class SVGGenerationStatus(BaseModel):
    session_id: str
    status: str  # "generating", "completed", "failed", "not_found"
    progress: float
    stage: str
    message: str
    generated_pages: Optional[List[Dict]] = None
    error: Optional[str] = None

@router.post("/convert-svg-to-ppt")
async def convert_svg_to_ppt(request: SVGToPPTRequest):
    """
    将SVG图片数据转换为PPT文件
    
    Args:
        request: 包含图片数据和元信息的请求
        
    Returns:
        PPT文件的二进制数据
    """
    try:
        logger.info(f"开始处理SVG转PPT请求，包含 {len(request.images)} 张图片")
        
        if not request.images:
            raise HTTPException(status_code=400, detail="图片数据不能为空")
        
        # 转换图片数据格式
        image_data_list = []
        for i, img in enumerate(request.images):
            image_data_list.append({
                "imageData": img.imageData,
                "format": img.format
            })
        
        # 创建SVG转PPT服务实例
        service = SVGToPPTService()
        
        # 生成PPT文件
        ppt_data = service.create_ppt_from_png_data(
            image_data_list=image_data_list,
            title=request.title,
            author=request.author
        )
        
        logger.info(f"成功生成PPT文件，大小: {len(ppt_data)} 字节")
        
        # 处理文件名编码问题（支持中文文件名）
        import urllib.parse
        safe_filename = urllib.parse.quote(f"{request.title}.pptx".encode('utf-8'))
        
        # 返回PPT文件
        return Response(
            content=ppt_data,
            media_type="application/vnd.openxmlformats-presentationml.presentation",
            headers={
                "Content-Disposition": f"attachment; filename*=UTF-8''{safe_filename}",
                "Content-Length": str(len(ppt_data))
            }
        )
        
    except Exception as e:
        logger.error(f"SVG转PPT处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"PPT生成失败: {str(e)}")

@router.post("/convert-svg-to-ppt-json")
async def convert_svg_to_ppt_json(request: SVGToPPTRequest):
    """
    将SVG图片数据转换为PPT文件，返回JSON格式的响应
    
    Args:
        request: 包含图片数据和元信息的请求
        
    Returns:
        JSON响应，包含成功状态和文件信息
    """
    try:
        logger.info(f"开始处理SVG转PPT请求 (JSON响应)，包含 {len(request.images)} 张图片")
        
        if not request.images:
            raise HTTPException(status_code=400, detail="图片数据不能为空")
        
        # 转换图片数据格式
        image_data_list = []
        for i, img in enumerate(request.images):
            image_data_list.append({
                "imageData": img.imageData,
                "format": img.format
            })
        
        # 创建SVG转PPT服务实例
        service = SVGToPPTService()
        
        # 生成PPT文件
        ppt_data = service.create_ppt_from_png_data(
            image_data_list=image_data_list,
            title=request.title,
            author=request.author
        )
        
        logger.info(f"成功生成PPT文件，大小: {len(ppt_data)} 字节")
        
        # 将PPT数据编码为base64
        import base64
        ppt_base64 = base64.b64encode(ppt_data).decode('utf-8')
        
        # 返回JSON响应
        return {
            "success": True,
            "message": "PPT生成成功",
            "data": {
                "title": request.title,
                "author": request.author,
                "totalSlides": len(request.images),
                "fileSize": len(ppt_data),
                "pptData": ppt_base64,
                "filename": f"{request.title}.pptx",
                "mimeType": "application/vnd.openxmlformats-presentationml.presentation"
            }
        }
        
    except Exception as e:
        logger.error(f"SVG转PPT处理失败: {str(e)}")
        return {
            "success": False,
            "message": f"PPT生成失败: {str(e)}",
            "data": None
        }

@router.get("/test-ppt-service")
async def test_ppt_service():
    """
    测试PPT服务是否正常工作
    """
    try:
        # 创建测试图片数据（1x1像素的透明PNG）
        test_image_data = [{
            "imageData": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
            "format": "png"
        }]
        
        # 创建服务实例并测试
        service = SVGToPPTService()
        ppt_data = service.create_ppt_from_png_data(test_image_data, "测试演示文稿")
        
        return {
            "success": True,
            "message": "PPT服务测试成功",
            "data": {
                "testFileSize": len(ppt_data),
                "serviceStatus": "正常"
            }
        }
        
    except Exception as e:
        logger.error(f"PPT服务测试失败: {str(e)}")
        return {
            "success": False,
            "message": f"PPT服务测试失败: {str(e)}",
            "data": None
        }

@router.websocket("/svg-progress/{session_id}")
async def websocket_svg_progress(websocket: WebSocket, session_id: str):
    """
    SVG生成进度WebSocket端点，支持重连状态同步
    
    Args:
        websocket: WebSocket连接
        session_id: 会话ID，用于识别生成任务
    """
    try:
        await manager.connect(websocket, session_id)
        logger.info(f"SVG进度WebSocket连接已建立，会话ID: {session_id}")
        
        # {{CHENGQI:
        # Action: Added
        # Timestamp: [2025-01-16T22:00:00+08:00]
        # Reason: 添加WebSocket重连时的状态同步功能，支持前端智能重连
        # Principle_Applied: 状态恢复 - WebSocket重连时自动同步当前状态; 用户体验 - 无缝重连体验
        # Optimization: 重连时立即发送当前状态和已生成页面
        # Architectural_Note (AR): 增强WebSocket连接的状态管理能力
        # Documentation_Note (DW): 配合前端重连机制，实现状态无缝恢复
        # }}
        
        # 如果会话已存在，发送当前状态（支持重连）
        if session_id in active_svg_sessions:
            try:
                session_data = active_svg_sessions[session_id]
                
                # 发送欢迎消息和当前状态
                welcome_data = {
                    "type": "svg_progress",
                    "stage": session_data.get("stage", "连接建立"),
                    "progress": session_data.get("progress", 0.0),
                    "message": f"WebSocket连接已建立 - {session_data.get('message', '等待进度更新')}",
                    "session_id": session_id,
                    "connection_type": "websocket_connected"
                }
                await websocket.send_text(json.dumps(welcome_data))
                
                # 如果有已生成的页面，快速发送页面状态（不逐个发送内容，减少连接时间）
                generated_pages = session_data.get("generated_pages", [])
                if generated_pages:
                    pages_summary = {
                        "type": "svg_pages_summary",
                        "stage": f"状态恢复",
                        "progress": session_data.get("progress", 0.0),
                        "message": f"已恢复 {len(generated_pages)} 个页面的状态",
                        "session_id": session_id,
                        "pages_count": len(generated_pages),
                        "pages_info": [
                            {
                                "page_number": page.get("page_number", i+1),
                                "title": page.get("title", f"幻灯片 {i+1}"),
                                "generated_at": page.get("generated_at")
                            } for i, page in enumerate(generated_pages)
                        ]
                    }
                    await websocket.send_text(json.dumps(pages_summary))
                    logger.info(f"WebSocket连接时发送页面状态摘要: {len(generated_pages)} 页")
                
            except Exception as sync_error:
                logger.warning(f"WebSocket连接时状态同步失败: {sync_error}")
        
        # 保持连接活跃，等待进度更新
        while True:
            try:
                # 接收来自客户端的消息（心跳等）
                data = await websocket.receive_text()
                message = json.loads(data)
                
                if message.get("type") == "ping":
                    # 回复心跳
                    await websocket.send_text(json.dumps({
                        "type": "pong",
                        "timestamp": message.get("timestamp")
                    }))
                    
            except WebSocketDisconnect:
                logger.info(f"SVG进度WebSocket连接断开，会话ID: {session_id}")
                break
            except json.JSONDecodeError:
                logger.warning(f"接收到无效的JSON消息，会话ID: {session_id}")
            except Exception as e:
                logger.error(f"WebSocket消息处理错误，会话ID: {session_id}, 错误: {e}")
                
    except Exception as e:
        logger.error(f"SVG进度WebSocket连接失败，会话ID: {session_id}, 错误: {e}")
    finally:
        await manager.disconnect(websocket)

@router.post("/generate-svg-with-progress")
async def generate_svg_with_progress(request: SVGGenerationRequest, background_tasks: BackgroundTasks):
    """
    启动SVG生成任务（立即返回，后台执行）
    
    Args:
        request: SVG生成请求，包含内容、配置和会话ID
        background_tasks: FastAPI后台任务管理器
        
    Returns:
        任务启动状态信息（立即返回）
    """
    try:
        logger.info(f"启动SVG生成任务，会话ID: {request.session_id}")
        
        if not request.content.strip():
            raise HTTPException(status_code=400, detail="内容不能为空")
        
        # 初始化会话状态
        session_data = {
            "status": "generating",
            "progress": 0.0,
            "stage": "任务已启动",
            "message": "正在后台生成SVG内容...",
            "generated_pages": [],
            "started_at": time.time(),
            "error": None,
            # 新增：保存生成上下文信息以支持单页重新生成
            "original_content": request.content,
            "style_config": request.style_config or {},
            "custom_requirements": request.custom_requirements,
            "design_spec": None,  # 将在生成完成后保存
            "svg_generator_config": None  # 保存生成器配置
        }
        active_svg_sessions[request.session_id] = session_data
        
        # 添加后台任务（不等待执行完成）
        background_tasks.add_task(
            _generate_svg_background_task,
            request.session_id,
            request.content,
            request.style_config or {},
            request.custom_requirements,
            request.template_set_name,
            request.color_theme
        )
        
        # 立即返回任务状态（5秒内完成）
        return {
            "success": True,
            "session_id": request.session_id,
            "status": "task_started",
            "message": "SVG生成任务已启动，请通过WebSocket接收进度更新",
            "websocket_url": f"/api/svg-ppt/svg-progress/{request.session_id}",
            "status_url": f"/api/svg-ppt/generation-status/{request.session_id}"
        }
        
    except Exception as e:
        logger.error(f"启动SVG生成任务失败: {str(e)}")
        # 更新会话状态为失败
        if request.session_id in active_svg_sessions:
            active_svg_sessions[request.session_id].update({
                "status": "failed",
                "error": str(e),
                "stage": "启动失败",
                "message": f"任务启动失败: {str(e)}"
            })
        raise HTTPException(status_code=500, detail=f"启动SVG生成任务失败: {str(e)}")

async def _generate_svg_background_task(session_id: str, content: str, style_config: dict, custom_requirements: str, template_set_name: str = None, color_theme: str = None):
    """
    后台SVG生成任务

    Args:
        session_id: 会话ID
        content: 源内容
        style_config: 样式配置
        custom_requirements: 自定义需求
        template_set_name: 选择的模板集名称
        color_theme: 选择的颜色主题
    """
    try:
        logger.info(f"开始执行后台SVG生成任务，会话ID: {session_id}")
        
        # 导入SVG生成器
        from backend.utils.svg_generator import AIPresenterSVGGenerator, GenerationConfig
        
        # 创建生成配置
        generation_config = GenerationConfig(
            model="gemini-2.5-flash",
            temperature=0.7,
            canvas_width=1920,
            canvas_height=1080,
            enable_streaming=False
        )
        
        # 根据style_config调整配置
        if style_config.get('svg_layout') == 'wide':
            generation_config.canvas_width = 2560
            generation_config.canvas_height = 1080
        elif style_config.get('svg_layout') == 'square':
            generation_config.canvas_width = 1080
            generation_config.canvas_height = 1080
        
        # 创建SVG生成器实例
        svg_generator = AIPresenterSVGGenerator(generation_config)
        
        # 创建异步WebSocket进度回调函数
        async def async_progress_callback(stage: str, progress: float, message: str = "", extra_data: Optional[Dict] = None):
            """异步进度回调函数，发送进度到WebSocket"""
            
            # 统一进度格式：如果progress是0-1小数，转换为0-100百分比
            if 0 <= progress <= 1:
                progress_percent = progress * 100
            else:
                # 如果已经是百分比格式，直接使用
                progress_percent = progress
            
            # 更新会话状态
            if session_id in active_svg_sessions:
                active_svg_sessions[session_id].update({
                    "stage": stage,
                    "progress": progress_percent,  # 使用统一的百分比格式
                    "message": message
                })
            
            # 发送WebSocket消息
            try:
                websocket = manager.session_connections.get(session_id)
                if websocket:
                    progress_data = {
                        "type": "svg_progress",
                        "stage": stage,
                        "progress": progress_percent,  # 使用统一的百分比格式
                        "message": message,
                        "session_id": session_id
                    }
                    
                    # 如果有额外数据，处理增量SVG页面完成事件
                    if extra_data and extra_data.get("type") == "svg_page_completed":
                        # 更新会话状态中的已生成页面
                        if session_id in active_svg_sessions:
                            if "generated_pages" not in active_svg_sessions[session_id]:
                                active_svg_sessions[session_id]["generated_pages"] = []
                            
                            page_data = extra_data.get("page_data", {})
                            active_svg_sessions[session_id]["generated_pages"].append(page_data)
                        
                        # 发送增量SVG页面数据
                        svg_page_data = {
                            "type": "svg_page_completed",
                            "stage": stage,
                            "progress": progress_percent,  # 使用统一的百分比格式
                            "message": message,
                            "session_id": session_id,
                            "page_data": extra_data.get("page_data", {}),
                            "progress_info": {
                                "completed_pages": len(active_svg_sessions[session_id]["generated_pages"]),
                                "total_pages": extra_data.get("total_pages", 1),
                                "percentage": progress_percent  # 使用统一的百分比格式
                            }
                        }
                        await websocket.send_text(json.dumps(svg_page_data))
                        logger.info(f"发送增量SVG页面: 第{page_data.get('page_number', '?')}页")
                    else:
                        # 发送普通进度更新
                        await websocket.send_text(json.dumps(progress_data))
                        logger.debug(f"发送进度更新: {stage} - {progress_percent}% - {message}")
            except Exception as e:
                logger.warning(f"发送进度更新失败，会话ID: {session_id}, 错误: {e}")
        
        # 设置异步进度回调
        svg_generator.set_progress_callback(async_progress_callback)

        # 发送开始信号
        await async_progress_callback("开始生成", 5.0, "正在初始化SVG生成器...")

        # 检查SVG生成方式
        svg_generation_mode = style_config.get('svg_generation_mode', 'free')

        # 添加调试日志
        logger.info(f"SVG生成参数检查: generation_mode={svg_generation_mode}, template_set_name={template_set_name}, color_theme={color_theme}")
        logger.info(f"style_config内容: {style_config}")

        if svg_generation_mode == 'template' and template_set_name:
            logger.info(f"✅ 使用模板集生成: {template_set_name}, 颜色主题: {color_theme}")
            await async_progress_callback("模板生成", 10.0, f"使用模板集: {template_set_name}")

            # 使用模板集生成SVG
            result = await _generate_svg_with_template_set(
                svg_generator=svg_generator,
                content=content,
                template_set_name=template_set_name,
                color_theme=color_theme,
                custom_requirements=custom_requirements,
                progress_callback=async_progress_callback
            )
        else:
            # 使用原有的自由生成方式
            logger.info("使用自由生成方式生成SVG")
            await async_progress_callback("自由生成", 10.0, "AI自由创作SVG图表和视觉元素")

            # 生成完整演示文稿（在后台执行，不影响HTTP响应）
            result = await svg_generator.generate_complete_presentation(
                content=content,
                custom_requirements=custom_requirements,
                style_config=style_config,
            )
        
        # 处理生成结果
        generated_pages = result.get('pages', [])
        has_generated_pages = len(generated_pages) > 0
        
        if result.get("success"):
            # 完全成功的情况
            active_svg_sessions[session_id].update({
                "status": "completed",
                "progress": 100.0,
                "stage": "生成完成",
                "message": f"成功生成 {len(generated_pages)} 页SVG",
                "generated_pages": generated_pages,
                "completed_at": time.time(),  # 记录完成时间
                # 保存设计规范和生成器配置以支持单页重新生成
                "design_spec": svg_generator.design_spec.__dict__ if svg_generator.design_spec else None,
                "svg_generator_config": {
                    "model": generation_config.model,
                    "temperature": generation_config.temperature,
                    "canvas_width": generation_config.canvas_width,
                    "canvas_height": generation_config.canvas_height,
                    "enable_streaming": generation_config.enable_streaming
                }
            })
            
            await async_progress_callback("生成完成", 100.0, f"成功生成 {len(generated_pages)} 页SVG")
            logger.info(f"后台SVG生成任务完成，会话ID: {session_id}, 生成页面数: {len(generated_pages)}")
            
            # 保存设计规范到文件系统
            if svg_generator.design_spec:
                save_design_spec_to_file(
                    session_id,
                    svg_generator.design_spec.__dict__,
                    {
                        "model": generation_config.model,
                        "temperature": generation_config.temperature,
                        "canvas_width": generation_config.canvas_width,
                        "canvas_height": generation_config.canvas_height,
                        "enable_streaming": generation_config.enable_streaming
                    },
                    content
                )
            
        elif has_generated_pages:
            # 部分成功的情况
            error_msg = result.get("error", "部分页面生成失败")
            active_svg_sessions[session_id].update({
                "status": "partial_completed",
                "progress": 80.0,
                "stage": "部分生成完成",
                "message": f"已生成 {len(generated_pages)} 页SVG，但部分页面失败",
                "generated_pages": generated_pages,
                "error": error_msg,
                "completed_at": time.time(),  # 记录完成时间
                # 即使部分成功也保存设计规范，以便重新生成失败的页面
                "design_spec": svg_generator.design_spec.__dict__ if svg_generator.design_spec else None,
                "svg_generator_config": {
                    "model": generation_config.model,
                    "temperature": generation_config.temperature,
                    "canvas_width": generation_config.canvas_width,
                    "canvas_height": generation_config.canvas_height,
                    "enable_streaming": generation_config.enable_streaming
                }
            })
            
            await async_progress_callback("部分生成完成", 80.0, f"已生成 {len(generated_pages)} 页，部分页面失败")
            logger.warning(f"后台SVG生成任务部分完成，会话ID: {session_id}, 错误: {error_msg}")
            
        else:
            # 完全失败的情况
            error_msg = result.get("error", "SVG生成失败")
            active_svg_sessions[session_id].update({
                "status": "failed",
                "progress": 0.0,
                "stage": "生成失败",
                "message": error_msg,
                "error": error_msg,
                "completed_at": time.time()  # 记录完成时间
            })
            
            await async_progress_callback("生成失败", 0.0, error_msg)
            logger.error(f"后台SVG生成任务失败，会话ID: {session_id}, 错误: {error_msg}")
            
    except Exception as e:
        logger.error(f"后台SVG生成任务异常，会话ID: {session_id}, 错误: {str(e)}")
        
        # 更新会话状态为异常
        if session_id in active_svg_sessions:
            active_svg_sessions[session_id].update({
                "status": "failed",
                "progress": 0.0,
                "stage": "执行异常",
                "message": f"任务执行异常: {str(e)}",
                "error": str(e),
                "completed_at": time.time()  # 记录异常完成时间
            })
            
            # 发送错误到WebSocket
            try:
                websocket = manager.session_connections.get(session_id)
                if websocket:
                    error_data = {
                        "type": "svg_error",
                        "error": str(e),
                        "session_id": session_id
                    }
                    await websocket.send_text(json.dumps(error_data))
            except Exception as ws_error:
                logger.error(f"发送错误消息到WebSocket失败: {ws_error}")

# 新增：SVG生成状态查询接口
@router.get("/generation-status/{session_id}")
async def get_svg_generation_status(session_id: str):
    """查询指定会话的SVG生成状态"""
    try:
        # 检查会话是否存在
        if session_id not in active_svg_sessions:
            return SVGGenerationStatus(
                session_id=session_id,
                status="not_found",
                progress=0.0,
                stage="",
                message="会话不存在或已结束"
            )
        
        session_data = active_svg_sessions[session_id]
        return SVGGenerationStatus(
            session_id=session_id,
            status=session_data.get("status", "unknown"),
            progress=session_data.get("progress", 0.0),
            stage=session_data.get("stage", ""),
            message=session_data.get("message", ""),
            generated_pages=session_data.get("generated_pages", []),
            error=session_data.get("error")
        )
        
    except Exception as e:
        logger.error(f"查询SVG生成状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"状态查询失败: {str(e)}")

# 新增：获取所有活跃的SVG生成会话
@router.get("/active-sessions")
async def get_active_svg_sessions():
    """获取所有活跃的SVG生成会话"""
    try:
        sessions = []
        for session_id, session_data in active_svg_sessions.items():
            sessions.append({
                "session_id": session_id,
                "status": session_data.get("status", "unknown"),
                "progress": session_data.get("progress", 0.0),
                "stage": session_data.get("stage", ""),
                "message": session_data.get("message", ""),
                "started_at": session_data.get("started_at"),
                "page_count": len(session_data.get("generated_pages", []))
            })
        
        return {
            "success": True,
            "active_sessions": sessions,
            "total_count": len(sessions)
        }
        
    except Exception as e:
        logger.error(f"获取活跃会话失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取会话列表失败: {str(e)}")

@router.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "健康",
        "serviceStatus": "正常"
    }

@router.get("/svg-templates")
async def get_svg_templates():
    """获取所有可用的SVG模板集"""
    try:
        import os
        import json

        templates_dir = os.path.join(os.getcwd(), "resources", "svg_templates")
        if not os.path.exists(templates_dir):
            return {
                "success": False,
                "error": "SVG模板目录不存在"
            }

        template_sets = []

        # 遍历模板目录
        for item in os.listdir(templates_dir):
            item_path = os.path.join(templates_dir, item)
            if os.path.isdir(item_path):
                # 查找set_info.json文件
                set_info_path = os.path.join(item_path, "set_info.json")
                if os.path.exists(set_info_path):
                    try:
                        with open(set_info_path, 'r', encoding='utf-8') as f:
                            set_info = json.load(f)

                        # 提取关键信息
                        template_set = {
                            "set_name": set_info.get("set_name", item),
                            "scenario": set_info.get("scenario", "未知"),
                            "style": set_info.get("style", "未知"),
                            "template_count": set_info.get("template_count", 0),
                            "created_at": set_info.get("created_at", ""),
                            "description": f"{set_info.get('scenario', '未知')} - {set_info.get('style', '未知')}风格",
                            "preview_info": {
                                "color_palette": set_info.get("combination_config", {}).get("colors", {}),
                                "visual_theme": set_info.get("design_specification", {}).get("theme_selection", {}).get("visual_theme", "")
                            }
                        }
                        template_sets.append(template_set)

                    except Exception as e:
                        logger.warning(f"解析模板集信息失败 {item}: {str(e)}")
                        # 如果解析失败，使用基本信息
                        template_sets.append({
                            "set_name": item,
                            "scenario": "未知",
                            "style": "未知",
                            "template_count": 0,
                            "created_at": "",
                            "description": f"模板集: {item}",
                            "preview_info": {}
                        })

        # 按创建时间排序（最新的在前）
        template_sets.sort(key=lambda x: x.get("created_at", ""), reverse=True)

        return {
            "success": True,
            "template_sets": template_sets,
            "total_count": len(template_sets)
        }

    except Exception as e:
        logger.error(f"获取SVG模板失败: {str(e)}")
        return {
            "success": False,
            "error": f"获取SVG模板失败: {str(e)}"
        }

@router.get("/template-preview/{template_set_name}")
async def get_template_preview(template_set_name: str):
    """获取模板集的预览SVG（第一个模板）"""
    try:
        import os
        import json

        templates_dir = os.path.join(os.getcwd(), "resources", "svg_templates")
        template_set_path = os.path.join(templates_dir, template_set_name)

        if not os.path.exists(template_set_path):
            return {
                "success": False,
                "error": f"模板集不存在: {template_set_name}"
            }

        # 读取模板集信息
        set_info_path = os.path.join(template_set_path, "set_info.json")
        if not os.path.exists(set_info_path):
            return {
                "success": False,
                "error": f"模板集信息文件不存在: {template_set_name}"
            }

        with open(set_info_path, 'r', encoding='utf-8') as f:
            set_info = json.load(f)

        templates = set_info.get("templates", [])
        if not templates:
            return {
                "success": False,
                "error": f"模板集中没有模板: {template_set_name}"
            }

        # 获取第一个模板作为预览
        first_template = templates[0]
        template_filename = first_template.get("filename", "")

        if not template_filename:
            return {
                "success": False,
                "error": f"第一个模板文件名为空: {template_set_name}"
            }

        # 读取第一个SVG文件
        template_path = os.path.join(template_set_path, template_filename)
        if not os.path.exists(template_path):
            return {
                "success": False,
                "error": f"模板文件不存在: {template_filename}"
            }

        with open(template_path, 'r', encoding='utf-8') as f:
            svg_content = f.read()

        return {
            "success": True,
            "preview_svg": svg_content,
            "template_info": {
                "filename": template_filename,
                "type": first_template.get("type", ""),
                "template_id": first_template.get("template_id", "")
            }
        }

    except Exception as e:
        logger.error(f"获取模板预览失败: {str(e)}")
        return {
            "success": False,
            "error": f"获取模板预览失败: {str(e)}"
        }

async def _generate_svg_with_template_set(svg_generator, content: str, template_set_name: str, color_theme: str, custom_requirements: str, progress_callback):
    """使用模板集生成SVG"""
    try:
        import os
        import json
        from backend.ai.openai_service import OpenAIService

        # 第一步：使用SVG生成器解析内容结构
        logger.info(f"🔍 开始解析内容结构，准备应用模板集: {template_set_name}")
        await progress_callback("内容解析", 5.0, "正在解析内容结构...")

        # 设置颜色主题到生成器
        svg_generator.user_color_theme = color_theme

        # 使用SVG生成器的设计规范生成功能来解析内容
        design_spec_result = await svg_generator.generate_design_specification(
            content=content,
            custom_requirements=custom_requirements,
            style_config={'svg_color_theme': color_theme}
        )

        if not design_spec_result.get("success", False):
            return {"success": False, "error": f"内容解析失败: {design_spec_result.get('error', '未知错误')}"}

        # 提取解析出的页面信息
        design_spec = design_spec_result.get("design_spec", {})
        parsed_pages = design_spec.get("content_outline", [])
        content_mapping = design_spec.get("content_mapping", {})

        if not parsed_pages:
            return {"success": False, "error": "内容解析未发现任何页面"}

        logger.info(f"✅ 内容解析完成，发现 {len(parsed_pages)} 个页面")
        logger.info(f"📝 内容映射包含 {len(content_mapping)} 个页面的详细内容")
        await progress_callback("内容解析完成", 10.0, f"解析出 {len(parsed_pages)} 个页面")

        # 第二步：加载模板集
        templates_dir = os.path.join(os.getcwd(), "resources", "svg_templates")
        template_set_path = os.path.join(templates_dir, template_set_name)

        if not os.path.exists(template_set_path):
            return {"success": False, "error": f"模板集不存在: {template_set_name}"}

        # 读取模板集信息
        set_info_path = os.path.join(template_set_path, "set_info.json")
        if not os.path.exists(set_info_path):
            return {"success": False, "error": f"模板集信息文件不存在: {template_set_name}"}

        with open(set_info_path, 'r', encoding='utf-8') as f:
            set_info = json.load(f)

        templates = set_info.get("templates", [])
        if not templates:
            return {"success": False, "error": f"模板集中没有模板: {template_set_name}"}

        logger.info(f"📋 加载模板集: {template_set_name}, 包含 {len(templates)} 个模板")
        await progress_callback("模板加载", 15.0, f"加载了 {len(templates)} 个模板")

        # 初始化AI服务
        ai_service = OpenAIService()
        ai_service.model = "gemini-2.5-flash"

        # 生成的页面列表
        generated_pages = []

        # 获取颜色调色板
        color_palette = _get_color_palette_for_theme(color_theme)

        # 第三步：将解析出的页面与模板匹配并生成（并发模式）
        total_pages = len(parsed_pages)
        logger.info(f"🎨 开始并发生成 {total_pages} 个页面，使用模板集: {template_set_name}")

        # 使用并发生成提升性能
        generated_pages = await _generate_pages_concurrently(
            parsed_pages=parsed_pages,
            content_mapping=content_mapping,
            templates=templates,
            template_set_path=template_set_path,
            ai_service=ai_service,
            color_palette=color_palette,
            custom_requirements=custom_requirements,
            progress_callback=progress_callback,
            total_pages=total_pages
        )

        # 过滤掉失败的页面
        successful_pages = [page for page in generated_pages if page is not None]

        await progress_callback("完成", 100.0, f"成功生成 {len(successful_pages)} 页SVG")

        return {
            "success": True,
            "pages": successful_pages,
            "total_pages": len(successful_pages)
        }

    except Exception as e:
        logger.error(f"使用模板集生成SVG失败: {str(e)}")
        return {"success": False, "error": f"使用模板集生成SVG失败: {str(e)}"}

async def _generate_pages_concurrently(parsed_pages, content_mapping, templates, template_set_path, ai_service, color_palette, custom_requirements, progress_callback, total_pages, max_concurrent=3):
    """并发生成多个页面"""
    import asyncio
    from asyncio import Semaphore

    # 创建信号量限制并发数量
    semaphore = Semaphore(max_concurrent)
    completed_count = 0

    async def generate_single_page(i, page_info):
        nonlocal completed_count

        async with semaphore:
            try:
                # 使用连续的页面编号，而不是原始的跳跃编号
                display_page_number = i + 1
                original_page_number = page_info.get("page_number", i + 1)
                page_title = page_info.get("title", f"页面 {display_page_number}")
                page_description = page_info.get("description", "")

                # 从content_mapping获取详细内容（使用原始页面编号）
                page_content_info = content_mapping.get(original_page_number, {})
                page_content = page_content_info.get("text", "")
                page_images = page_content_info.get("images", [])

                # 如果没有详细内容，使用标题和描述作为内容
                if not page_content:
                    page_content = f"{page_title}\n{page_description}"

                # 从标题推断页面类型
                page_type = _infer_page_type_from_title(page_title)

                logger.info(f"📄 [并发] 准备生成页面 {display_page_number}: {page_title} (原编号: {original_page_number})")

                # 根据页面类型选择合适的模板
                selected_template = _select_template_for_page(templates, page_type, display_page_number, total_pages)

                if not selected_template:
                    logger.warning(f"未找到适合的模板，跳过页面 {display_page_number}: {page_title}")
                    return None

                template_filename = selected_template.get("filename", "")
                template_type = selected_template.get("type", "")

                # 加载SVG模板
                template_path = os.path.join(template_set_path, template_filename)
                if not os.path.exists(template_path):
                    logger.warning(f"模板文件不存在: {template_path}")
                    return None

                with open(template_path, 'r', encoding='utf-8') as f:
                    template_svg = f.read()

                logger.info(f"📄 [并发] 处理页面 {display_page_number}: {page_title} -> 使用模板: {template_type}")

                # 使用AI生成内容并修改SVG
                modified_svg = await _modify_svg_with_ai(
                    ai_service=ai_service,
                    template_svg=template_svg,
                    page_content=page_content,
                    page_title=page_title,
                    page_images=page_images,
                    template_type=template_type,
                    page_number=display_page_number,
                    color_palette=color_palette,
                    custom_requirements=custom_requirements,
                    total_pages=total_pages
                )

                if modified_svg:
                    page_data = {
                        "page_number": display_page_number,
                        "title": page_title,
                        "content": modified_svg,
                        "template_type": selected_template.get("template_id", ""),
                        "svg_code": modified_svg
                    }

                    # 更新完成计数并发送进度
                    completed_count += 1
                    progress_percentage = 20.0 + (completed_count / total_pages) * 70.0

                    await progress_callback("页面完成", progress_percentage, f"第 {display_page_number} 页生成完成: {page_title}", {
                        "type": "svg_page_completed",
                        "page_data": page_data,
                        "total_pages": total_pages
                    })

                    logger.info(f"✅ [并发] 页面 {display_page_number} 生成完成: {page_title}")
                    return page_data

                return None

            except Exception as e:
                logger.error(f"❌ [并发] 页面 {i+1} 生成失败: {str(e)}")
                return None

    # 创建所有页面的生成任务
    tasks = [generate_single_page(i, page_info) for i, page_info in enumerate(parsed_pages)]

    # 并发执行所有任务
    logger.info(f"🚀 启动 {len(tasks)} 个并发任务，最大并发数: {max_concurrent}")
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # 处理结果，过滤异常
    successful_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            logger.error(f"❌ 页面 {i+1} 生成异常: {str(result)}")
        elif result is not None:
            successful_results.append(result)

    # 按页面编号排序，确保顺序正确
    successful_results.sort(key=lambda x: x["page_number"])

    logger.info(f"🎉 并发生成完成: {len(successful_results)}/{len(parsed_pages)} 页成功")
    return successful_results

# 保留原有的串行生成逻辑作为备用
async def _generate_pages_sequentially_backup(parsed_pages, content_mapping, templates, template_set_path, ai_service, color_palette, custom_requirements, progress_callback, total_pages):
    """串行生成多个页面（备用方案）"""
    generated_pages = []

    for i, page_info in enumerate(parsed_pages):
            # 使用连续的页面编号，而不是原始的跳跃编号
            display_page_number = i + 1
            original_page_number = page_info.get("page_number", i + 1)
            page_title = page_info.get("title", f"页面 {display_page_number}")
            page_description = page_info.get("description", "")

            # 从content_mapping获取详细内容（使用原始页面编号）
            page_content_info = content_mapping.get(original_page_number, {})
            page_content = page_content_info.get("text", "")
            page_images = page_content_info.get("images", [])

            # 如果没有详细内容，使用标题和描述作为内容
            if not page_content:
                page_content = f"{page_title}\n{page_description}"

            # 从标题推断页面类型
            page_type = _infer_page_type_from_title(page_title)

            logger.info(f"📄 准备生成页面 {display_page_number}: {page_title} (原编号: {original_page_number})")
            logger.info(f"📝 页面内容长度: {len(page_content)} 字符")
            logger.info(f"🖼️ 页面图片数量: {len(page_images)} 张")
            logger.info(f"🏷️ 页面类型: {page_type}")

            # 如果页面有图片，记录图片信息
            if page_images:
                for idx, img in enumerate(page_images[:3]):  # 只显示前3张图片的信息
                    logger.info(f"   图片{idx+1}: {img.get('url', 'N/A')[:100]}...")

            await progress_callback("生成页面", 20.0 + (i / total_pages) * 70.0, f"生成第 {display_page_number} 页: {page_title}")

            # 根据页面类型选择合适的模板
            selected_template = _select_template_for_page(templates, page_type, display_page_number, total_pages)

            if not selected_template:
                logger.warning(f"未找到适合的模板，跳过页面 {display_page_number}: {page_title}")
                continue

            template_filename = selected_template.get("filename", "")
            template_type = selected_template.get("type", "")

            # 加载SVG模板
            template_path = os.path.join(template_set_path, template_filename)
            if not os.path.exists(template_path):
                logger.warning(f"模板文件不存在: {template_path}")
                continue

            with open(template_path, 'r', encoding='utf-8') as f:
                template_svg = f.read()

            logger.info(f"📄 处理页面 {display_page_number}: {page_title} -> 使用模板: {template_type}")

            # 使用AI生成内容并修改SVG
            modified_svg = await _modify_svg_with_ai(
                ai_service=ai_service,
                template_svg=template_svg,
                page_content=page_content,  # 使用解析出的页面内容
                page_title=page_title,      # 使用解析出的页面标题
                page_images=page_images,    # 传递页面图片信息
                template_type=template_type,
                page_number=display_page_number,  # 使用连续的页面编号
                color_palette=color_palette,
                custom_requirements=custom_requirements,
                total_pages=total_pages
            )

            if modified_svg:
                page_data = {
                    "page_number": display_page_number,  # 使用连续的页面编号
                    "title": page_title,  # 使用解析出的标题
                    "content": modified_svg,
                    "template_type": selected_template.get("template_id", ""),
                    "svg_code": modified_svg
                }
                generated_pages.append(page_data)

                # 发送单页完成的WebSocket消息（模拟自由生成的行为）
                await progress_callback("页面完成", 20.0 + ((i + 1) / total_pages) * 70.0, f"第 {display_page_number} 页生成完成: {page_title}", {
                    "type": "svg_page_completed",
                    "page_data": page_data,
                    "total_pages": total_pages
                })

def _infer_page_type_from_title(page_title: str) -> str:
    """从页面标题推断页面类型"""
    title_lower = page_title.lower()

    # 封面页关键词
    if any(keyword in title_lower for keyword in ['封面', 'cover', '标题', 'title']):
        return 'cover'

    # 目录页关键词
    if any(keyword in title_lower for keyword in ['目录', 'contents', 'toc', 'index', '结构']):
        return 'toc'

    # 总结页关键词
    if any(keyword in title_lower for keyword in ['总结', 'conclusion', 'summary', '结论']):
        return 'summary'

    # 参考文献关键词
    if any(keyword in title_lower for keyword in ['参考', 'references', 'bibliography', '文献', '资料']):
        return 'reference'

    # 数据页关键词
    if any(keyword in title_lower for keyword in ['数据', 'data', '图表', 'chart', '统计', 'statistics']):
        return 'data'

    # 默认为内容页
    return 'content'

def _select_template_for_page(templates: list, page_type: str, page_number: int, total_pages: int) -> dict:
    """根据页面类型和位置选择合适的模板"""

    # 定义页面类型映射
    type_mapping = {
        'cover': ['封面', 'title', 'cover'],
        'toc': ['目录', 'contents', 'toc', 'index'],
        'content': ['内容', 'content', 'text'],
        'data': ['数据', 'chart', 'graph', 'statistics'],
        'summary': ['总结', 'conclusion', 'summary'],
        'reference': ['参考', 'references', 'bibliography']
    }

    # 根据页面位置推断类型
    if page_number == 1:
        preferred_types = type_mapping['cover']
    elif page_number == 2 and total_pages > 3:
        preferred_types = type_mapping['toc']
    elif page_number == total_pages:
        preferred_types = type_mapping['reference']
    elif page_number == total_pages - 1 and total_pages > 5:
        preferred_types = type_mapping['summary']
    else:
        preferred_types = type_mapping['content']

    # 首先尝试精确匹配
    for template in templates:
        template_type = template.get("type", "").lower()
        if any(ptype in template_type for ptype in preferred_types):
            return template

    # 如果没有精确匹配，使用第一个可用模板
    if templates:
        return templates[0]

    return None

def _get_color_palette_for_theme(color_theme: str) -> Dict[str, str]:
    """根据颜色主题获取调色板"""
    color_palettes = {
        'golden': {'primary': '#FFD700', 'accent': '#FFA500', 'secondary': '#DAA520'},
        'bright_yellow': {'primary': '#FFFF00', 'accent': '#FFD700', 'secondary': '#FFF700'},
        'light_green': {'primary': '#90EE90', 'accent': '#98FB98', 'secondary': '#00FA9A'},
        'pink': {'primary': '#FFC0CB', 'accent': '#FFB6C1', 'secondary': '#FF69B4'},
        'cyan': {'primary': '#00CED1', 'accent': '#40E0D0', 'secondary': '#48D1CC'},
        'purple': {'primary': '#8A2BE2', 'accent': '#9370DB', 'secondary': '#BA55D3'},
        'blue': {'primary': '#4169E1', 'accent': '#1E90FF', 'secondary': '#0000FF'},
        'brown': {'primary': '#A0522D', 'accent': '#8B4513', 'secondary': '#D2691E'},
        'red': {'primary': '#DC143C', 'accent': '#FF0000', 'secondary': '#B22222'}
    }

    return color_palettes.get(color_theme, color_palettes['blue'])

# 导入统一的图片处理器
from backend.utils.image_processor import image_processor

async def _modify_svg_with_ai(ai_service, template_svg: str, page_content: str, page_title: str, page_images: list, template_type: str, page_number: int, color_palette: Dict[str, str], custom_requirements: str, total_pages: int = 1) -> str:
    """使用AI修改SVG模板内容"""
    try:
        # 验证并过滤有效的图片
        logger.info(f"🔍 开始验证页面图片，原始图片数量: {len(page_images)}")
        valid_images = await image_processor.validate_and_filter_images(page_images)

        # 构建图片信息部分
        images_info = ""
        if valid_images:
            images_info = "\n## 页面相关图片\n"
            for i, img in enumerate(valid_images[:5]):  # 最多显示5张有效图片
                img_url = img.get('url', '')
                img_alt = img.get('alt', f'图片{i+1}')
                images_info += f"图片{i+1}: {img_alt}\nURL: {img_url}\n\n"
            images_info += "**重要提示**: 请在SVG中为这些图片预留位置，使用 <image> 标签引用图片URL，或者创建图片占位符。\n"
            logger.info(f"✅ 将使用 {len(valid_images)} 张有效图片生成SVG")
        else:
            images_info = "\n## 页面相关图片\n暂无有效图片，可以根据内容创建相关的图标或图形元素。\n"
            logger.info("ℹ️ 没有有效图片，将使用图标或图形元素")

        # 构建AI提示词
        prompt = f"""# SVG模板内容生成任务

## 任务目标
基于解析出的页面内容，直接修改SVG模板，生成第{page_number}页：{page_title}

## 页面标题
{page_title}

## 页面内容
{page_content}

## 模板类型
{template_type}
{images_info}
## 颜色主题配置
主色调: {color_palette.get('primary', '#4169E1')}
辅助色: {color_palette.get('accent', '#1E90FF')}
次要色: {color_palette.get('secondary', '#0000FF')}

**颜色应用规则：**
1. **背景色应用**：将主色调应用于背景、装饰元素、图形边框等
2. **文字颜色优化**：
   - 浅色背景 → 使用深色文字 (#333333, #000000)
   - 深色背景 → 使用浅色文字 (#FFFFFF, #F8F8F8)
   - 彩色背景 → 根据背景亮度自动选择对比度高的文字颜色
3. **对比度保证**：确保文字与背景的对比度足够高，保证可读性
4. **层次分明**：
   - 标题：使用主色调或高对比度颜色
   - 正文：使用中性色（黑色/白色/深灰）
   - 强调文字：使用辅助色，但要确保可读性

## 自定义要求
{custom_requirements}

## SVG模板代码
```svg
{template_svg}
```

## 生成要求
1. **直接修改SVG内容**：将模板中的文本内容替换为基于用户内容生成的具体内容
2. **保持SVG结构**：不要改变SVG的基本结构、样式和布局
3. **智能颜色应用**：
   - 将用户选择的颜色主题应用于背景、装饰元素、图形等
   - **重要**：根据背景颜色智能调整文字颜色，确保高对比度和可读性
   - 浅色背景使用深色文字，深色背景使用浅色文字
   - 避免使用相近颜色导致文字看不清
4. **字体和文字优化**：
   - 标题文字：可以使用主色调，但要确保与背景有足够对比度
   - 正文文字：优先使用黑色(#000000)或白色(#FFFFFF)，确保最佳可读性
   - 重要信息：可以使用辅助色突出显示，但要保证可读性
5. **专业性**：内容要专业、准确、适合演示
6. **图片集成**：如果有相关图片，请在SVG中使用 `<image>` 标签引用图片URL，或创建相应的图形占位符

## 页面特定指导
- 封面页：提取主标题、副标题、作者等基础信息，如有相关图片可作为背景或装饰
- 目录页：根据用户内容生成章节目录，可添加图标或小图片
- 内容页：提取核心内容和要点，相关图片可作为内容说明
- 数据页：提取数据、统计信息或关键指标，图片可用于数据可视化
- 总结页：总结核心观点和结论，图片可作为总结的视觉支持

## 图片处理指导
- 使用 `<image x="..." y="..." width="..." height="..." href="图片URL"/>` 标签
- 图片尺寸要适合页面布局，建议宽度不超过400px
- 为图片添加合适的位置和边框样式
- 如果图片URL无效，可创建相应的图形占位符

## 颜色对比度示例
**正确示例：**
- 深蓝背景(#4169E1) + 白色文字(#FFFFFF) ✓
- 浅色背景(#F0F8FF) + 深色文字(#333333) ✓
- 金黄背景(#FFD700) + 深色文字(#000000) ✓

**错误示例（避免）：**
- 蓝色背景(#4169E1) + 紫色文字(#8A2BE2) ✗ (对比度不足)
- 黄色背景(#FFFF00) + 白色文字(#FFFFFF) ✗ (看不清)
- 红色背景(#DC143C) + 粉色文字(#FFC0CB) ✗ (冲突)

## 输出要求
请直接输出修改后的完整SVG代码，不要添加任何解释文字。

开始生成："""

        # 调用AI生成
        response = await ai_service.chat_completion(
            messages=[{"role": "user", "content": prompt}],
            temperature=0.7
        )

        if "error" in response:
            logger.error(f"AI生成SVG失败: {response['error']}")
            return template_svg  # 返回原模板

        # 提取SVG代码
        generated_svg = _extract_svg_from_response(response["content"])
        if not generated_svg:
            logger.warning("未能提取到有效的SVG代码，使用原模板")
            return template_svg

        return generated_svg

    except Exception as e:
        logger.error(f"AI修改SVG失败: {str(e)}")
        return template_svg  # 返回原模板

def _extract_svg_from_response(response_content: str) -> str:
    """从AI响应中提取SVG代码"""
    try:
        import re

        # 尝试提取SVG代码块
        svg_patterns = [
            r'```svg\s*(.*?)\s*```',
            r'```xml\s*(.*?)\s*```',
            r'```\s*(.*?)\s*```',
            r'(<svg.*?</svg>)'
        ]

        for pattern in svg_patterns:
            match = re.search(pattern, response_content, re.DOTALL)
            if match:
                svg_content = match.group(1)
                # 确保SVG标签完整
                if '<svg' in svg_content and '</svg>' in svg_content:
                    return svg_content.strip()

        # 如果没有找到代码块，检查是否整个响应就是SVG
        if '<svg' in response_content and '</svg>' in response_content:
            start = response_content.find('<svg')
            end = response_content.find('</svg>') + 6
            return response_content[start:end].strip()

        return ""

    except Exception as e:
        logger.warning(f"提取SVG代码失败: {str(e)}")
        return ""

# 新增：获取已生成的SVG页面
@router.get("/generated-pages/{session_id}")
async def get_generated_pages(session_id: str):
    """获取指定会话的已生成SVG页面"""
    try:
        # 检查会话是否存在
        if session_id not in active_svg_sessions:
            raise HTTPException(
                status_code=404, 
                detail=f"会话 {session_id} 不存在或已清理"
            )
        
        session_data = active_svg_sessions[session_id]
        generated_pages = session_data.get("generated_pages", [])
        
        return {
            "success": True,
            "session_id": session_id,
            "pages": generated_pages,
            "total_pages": len(generated_pages),
            "status": session_data.get("status", "unknown"),
            "progress": session_data.get("progress", 0.0),
            "message": f"已获取 {len(generated_pages)} 个已生成的页面"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取已生成页面失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取页面失败: {str(e)}")

# 新增：WebSocket重连时的状态同步
@router.post("/reconnect-session/{session_id}")
async def reconnect_session(session_id: str):
    """
    WebSocket重连时的状态同步，发送当前状态
    """
    try:
        # 检查会话是否存在
        if session_id not in active_svg_sessions:
            return {
                "success": False,
                "message": "会话不存在或已清理",
                "status": "not_found"
            }
        
        session_data = active_svg_sessions[session_id]
        
        # 如果有WebSocket连接，发送当前状态
        websocket = manager.session_connections.get(session_id)
        if websocket:
            try:
                # 发送当前整体状态
                status_data = {
                    "type": "svg_progress",
                    "stage": session_data.get("stage", ""),
                    "progress": session_data.get("progress", 0.0),
                    "message": f"重连成功 - {session_data.get('message', '')}",
                    "session_id": session_id,
                    "reconnected": True
                }
                await websocket.send_text(json.dumps(status_data))
                
                # 如果有已生成的页面，逐个发送增量页面事件
                generated_pages = session_data.get("generated_pages", [])
                for i, page in enumerate(generated_pages):
                    page_data = {
                        "type": "svg_page_completed",
                        "stage": f"页面恢复 ({i+1}/{len(generated_pages)})",
                        "progress": session_data.get("progress", 0.0),
                        "message": f"恢复第 {page.get('page_number', i+1)} 页",
                        "session_id": session_id,
                        "page_data": page,
                        "progress_info": {
                            "completed_pages": i + 1,
                            "total_pages": len(generated_pages),
                            "percentage": session_data.get("progress", 0.0)
                        },
                        "reconnected": True
                    }
                    await websocket.send_text(json.dumps(page_data))
                    
                    # 避免消息发送过快
                    await asyncio.sleep(0.1)
                
                logger.info(f"WebSocket重连状态同步完成，会话ID: {session_id}, 恢复 {len(generated_pages)} 页")
                
            except Exception as ws_error:
                logger.error(f"WebSocket状态同步失败: {ws_error}")
        
        return {
            "success": True,
            "session_id": session_id,
            "status": session_data.get("status", "unknown"),
            "progress": session_data.get("progress", 0.0),
            "stage": session_data.get("stage", ""),
            "restored_pages": len(session_data.get("generated_pages", [])),
            "message": "重连状态同步完成"
        }
        
    except Exception as e:
        logger.error(f"重连会话失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"重连失败: {str(e)}")

# 新增：任务清理机制
async def cleanup_old_sessions():
    """清理过期的任务会话"""
    try:
        current_time = time.time()
        sessions_to_remove = []
        
        for session_id, session_data in active_svg_sessions.items():
            task_status = session_data.get("status", "generating")
            
            # 根据任务状态确定基准时间和保持期限
            if task_status in ["completed", "failed", "partial_completed"]:
                # 已完成的任务：从完成时间开始计算，保持30分钟
                base_time = session_data.get("completed_at", session_data.get("started_at", current_time))
                keep_duration = TASK_KEEP_DURATION_COMPLETED
            else:
                # 活跃任务：从开始时间计算，保持1小时
                base_time = session_data.get("started_at", current_time)
                keep_duration = TASK_KEEP_DURATION_ACTIVE
            
            age = current_time - base_time
            
            # 如果任务超过保持期限，标记为待清理
            if age > keep_duration:
                sessions_to_remove.append(session_id)
                logger.debug(f"标记清理任务: {session_id}, 状态: {task_status}, 存在时间: {age:.0f}秒, 保持期限: {keep_duration}秒")
        
        # 清理过期会话
        for session_id in sessions_to_remove:
            logger.info(f"清理过期任务会话: {session_id}")
            del active_svg_sessions[session_id]
            
            # 如果有WebSocket连接，通知客户端任务已清理
            websocket = manager.session_connections.get(session_id)
            if websocket:
                try:
                    cleanup_data = {
                        "type": "session_cleanup",
                        "message": "任务会话已清理，请重新开始",
                        "session_id": session_id
                    }
                    await websocket.send_text(json.dumps(cleanup_data))
                except Exception:
                    pass  # 忽略发送失败，可能连接已断开
        
        if sessions_to_remove:
            logger.info(f"任务清理完成，共清理 {len(sessions_to_remove)} 个过期会话")
            
    except Exception as e:
        logger.error(f"任务清理失败: {str(e)}")

# 启动任务清理的后台任务
async def start_cleanup_task():
    """启动定期清理任务"""
    while True:
        try:
            await asyncio.sleep(TASK_CLEANUP_INTERVAL)
            await cleanup_old_sessions()
        except Exception as e:
            logger.error(f"定期清理任务异常: {str(e)}")
            await asyncio.sleep(60)  # 出错后1分钟后重试

# 在模块加载时启动清理任务（可选，需要在应用启动时调用）
# asyncio.create_task(start_cleanup_task())

# 新增：设计规范持久化存储路径
import os
DESIGN_SPEC_CACHE_DIR = os.path.join(os.path.expanduser("~"), ".jimu", "design_specs")
os.makedirs(DESIGN_SPEC_CACHE_DIR, exist_ok=True)

def save_design_spec_to_file(session_id: str, design_spec: dict, generator_config: dict, content: str):
    """保存设计规范到文件系统"""
    try:
        cache_file = os.path.join(DESIGN_SPEC_CACHE_DIR, f"{session_id}.json")
        cache_data = {
            "design_spec": design_spec,
            "generator_config": generator_config,
            "content": content,
            "timestamp": time.time(),
            "version": "1.0"
        }
        with open(cache_file, 'w', encoding='utf-8') as f:
            json.dump(cache_data, f, ensure_ascii=False, indent=2)
        logger.info(f"设计规范已保存到文件: {cache_file}")
        return True
    except Exception as e:
        logger.error(f"保存设计规范到文件失败: {e}")
        return False

def load_design_spec_from_file(session_id: str) -> Optional[Dict]:
    """从文件系统加载设计规范"""
    try:
        cache_file = os.path.join(DESIGN_SPEC_CACHE_DIR, f"{session_id}.json")
        if os.path.exists(cache_file):
            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            # 检查文件是否过期（24小时）
            if time.time() - cache_data.get("timestamp", 0) < 24 * 3600:
                logger.info(f"从文件加载设计规范: {cache_file}")
                return cache_data
            else:
                # 删除过期文件
                os.remove(cache_file)
                logger.info(f"删除过期的设计规范文件: {cache_file}")
        return None
    except Exception as e:
        logger.error(f"从文件加载设计规范失败: {e}")
        return None

@router.post("/regenerate-single-svg")
async def regenerate_single_svg(request: SingleSVGRegenerationRequest, background_tasks: BackgroundTasks):
    """
    重新生成单张SVG页面
    
    Args:
        request: 单张SVG重新生成请求
        background_tasks: FastAPI后台任务管理器
        
    Returns:
        重新生成任务状态信息
    """
    try:
        logger.info(f"启动单张SVG重新生成任务，会话ID: {request.session_id}, 页面索引: {request.page_index}")
        
        if not request.content.strip():
            raise HTTPException(status_code=400, detail="内容不能为空")
        
        if request.page_index < 0:
            raise HTTPException(status_code=400, detail="页面索引必须大于等于0")
        
        # 获取或创建会话状态
        if request.session_id not in active_svg_sessions:
            # 如果会话不存在，创建新会话
            active_svg_sessions[request.session_id] = {
                "status": "regenerating",
                "progress": 0.0,
                "stage": "重新生成中",
                "message": f"正在重新生成第 {request.page_index + 1} 页...",
                "generated_pages": [],
                "started_at": time.time(),
                "error": None
            }
        else:
            # 更新现有会话状态
            active_svg_sessions[request.session_id].update({
                "status": "regenerating",
                "stage": "重新生成中",
                "message": f"正在重新生成第 {request.page_index + 1} 页..."
            })
        
        # 添加后台任务（不等待执行完成）
        background_tasks.add_task(
            _regenerate_single_svg_background_task,
            request.session_id,
            request.content,
            request.page_index,
            request.page_title,
            request.style_config or {},
            request.custom_requirements,
            request.original_session_id
        )
        
        # 立即返回任务状态
        return {
            "success": True,
            "session_id": request.session_id,
            "page_index": request.page_index,
            "status": "regeneration_started",
            "message": f"第 {request.page_index + 1} 页重新生成任务已启动，请通过WebSocket接收进度更新",
            "websocket_url": f"/api/svg-ppt/svg-progress/{request.session_id}"
        }
        
    except Exception as e:
        logger.error(f"启动单张SVG重新生成任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"启动重新生成任务失败: {str(e)}")

async def _regenerate_single_svg_background_task(session_id: str, content: str, page_index: int, page_title: str, style_config: dict, custom_requirements: str, original_session_id: str = ""):
    """
    后台单张SVG重新生成任务
    
    Args:
        session_id: 会话ID
        content: 源内容
        page_index: 要重新生成的页面索引
        page_title: 页面标题
        style_config: 样式配置
        custom_requirements: 自定义需求
    """
    try:
        logger.info(f"开始执行单张SVG重新生成任务，会话ID: {session_id}, 页面索引: {page_index}")
        
        # 导入SVG生成器
        from backend.utils.svg_generator import AIPresenterSVGGenerator, GenerationConfig
        
        # 创建生成配置
        generation_config = GenerationConfig(
            model="gemini-2.5-flash",
            temperature=0.7,
            canvas_width=1920,
            canvas_height=1080,
            enable_streaming=False
        )
        
        # 根据style_config调整配置
        if style_config.get('svg_layout') == 'wide':
            generation_config.canvas_width = 2560
            generation_config.canvas_height = 1080
        elif style_config.get('svg_layout') == 'square':
            generation_config.canvas_width = 1080
            generation_config.canvas_height = 1080
        
        # 创建SVG生成器实例
        svg_generator = AIPresenterSVGGenerator(generation_config)
        
        # 创建异步WebSocket进度回调函数
        async def async_progress_callback(stage: str, progress: float, message: str = "", extra_data: Optional[Dict] = None):
            """异步进度回调函数，发送进度到WebSocket"""
            
            # 统一进度格式：如果progress是0-1小数，转换为0-100百分比
            if 0 <= progress <= 1:
                progress_percent = progress * 100
            else:
                progress_percent = progress
            
            # 更新会话状态
            if session_id in active_svg_sessions:
                active_svg_sessions[session_id].update({
                    "stage": stage,
                    "progress": progress_percent,
                    "message": message
                })
            
            # 发送WebSocket消息
            try:
                websocket = manager.session_connections.get(session_id)
                if websocket:
                    progress_data = {
                        "type": "svg_single_regeneration_progress",
                        "stage": stage,
                        "progress": progress_percent,
                        "message": message,
                        "session_id": session_id,
                        "page_index": page_index,
                        "page_title": page_title
                    }
                    
                    await websocket.send_text(json.dumps(progress_data))
                    logger.debug(f"发送单页重新生成进度更新: {stage} - {progress_percent}% - {message}")
            except Exception as e:
                logger.warning(f"发送进度更新失败，会话ID: {session_id}, 错误: {e}")
        
        # 设置异步进度回调
        svg_generator.set_progress_callback(async_progress_callback)
        
        # 发送开始信号
        await async_progress_callback("开始重新生成", 10.0, f"正在重新生成第 {page_index + 1} 页...")
        
        # 改进的实现：智能获取设计规范重新生成单个页面
        
        # 多重策略获取设计规范
        saved_design_spec = None
        saved_generator_config = None
        source_session_id = None
        
        # 策略1：从当前会话获取
        session_data = active_svg_sessions.get(session_id, {})
        if session_data.get("design_spec"):
            saved_design_spec = session_data.get("design_spec")
            saved_generator_config = session_data.get("svg_generator_config")
            source_session_id = session_id
            logger.info(f"从当前会话 {session_id} 获取到设计规范")
        
        # 策略2：从原始会话获取
        elif original_session_id and original_session_id in active_svg_sessions:
            original_session_data = active_svg_sessions[original_session_id]
            if original_session_data.get("design_spec"):
                saved_design_spec = original_session_data.get("design_spec")
                saved_generator_config = original_session_data.get("svg_generator_config")
                source_session_id = original_session_id
                logger.info(f"从原始会话 {original_session_id} 获取到设计规范")
                
                # 复制设计规范到当前会话，避免下次重复查找
                active_svg_sessions[session_id].update({
                    "design_spec": saved_design_spec,
                    "svg_generator_config": saved_generator_config,
                    "original_content": original_session_data.get("original_content", content),
                    "style_config": original_session_data.get("style_config", style_config)
                })
        
        # 策略3：从文件系统加载设计规范
        elif not saved_design_spec and original_session_id:
            logger.info(f"尝试从文件系统加载设计规范: {original_session_id}")
            file_cache_data = load_design_spec_from_file(original_session_id)
            if file_cache_data and file_cache_data.get("content") == content:
                saved_design_spec = file_cache_data.get("design_spec")
                saved_generator_config = file_cache_data.get("generator_config")
                source_session_id = original_session_id
                logger.info(f"从文件系统获取到设计规范: {original_session_id}")
                
                # 恢复到内存会话中
                active_svg_sessions[session_id].update({
                    "design_spec": saved_design_spec,
                    "svg_generator_config": saved_generator_config,
                    "original_content": content,
                    "style_config": style_config
                })
        
        # 策略4：搜索所有会话中相同内容的设计规范
        elif not saved_design_spec:
            logger.info("搜索其他会话中的设计规范...")
            for other_session_id, other_session_data in active_svg_sessions.items():
                if (other_session_data.get("design_spec") and 
                    other_session_data.get("original_content") == content):
                    saved_design_spec = other_session_data.get("design_spec")
                    saved_generator_config = other_session_data.get("svg_generator_config")
                    source_session_id = other_session_id
                    logger.info(f"从相同内容会话 {other_session_id} 获取到设计规范")
                    
                    # 复制到当前会话
                    active_svg_sessions[session_id].update({
                        "design_spec": saved_design_spec,
                        "svg_generator_config": saved_generator_config,
                        "original_content": content,
                        "style_config": other_session_data.get("style_config", style_config)
                    })
                    break
        
        if not saved_design_spec:
            # 如果没有保存的设计规范，回退到完整生成模式
            logger.warning(f"会话 {session_id} 没有保存的设计规范，使用完整生成模式")
            await async_progress_callback("准备重新生成", 20.0, "没有设计规范，正在重新分析内容...")
            
            result = await svg_generator.generate_complete_presentation(
                content=content,
                custom_requirements=f"请特别关注第 {page_index + 1} 页的内容生成，标题为：{page_title}。{custom_requirements}",
                style_config=style_config,
            )
            
            # 从结果中提取指定页面
            single_page_result = {"success": False, "page_data": None}
            if result.get("success") and result.get("pages"):
                pages = result["pages"]
                if page_index < len(pages):
                    target_page = pages[page_index]
                    single_page_result = {
                        "success": True,
                        "page_data": {
                            "page_number": page_index + 1,
                            "title": page_title or target_page.get("title", f"幻灯片 {page_index + 1}"),
                            "svg_code": target_page.get("content", target_page.get("svg_code", "")),
                            "content": target_page.get("content", target_page.get("svg_code", "")),
                            "generated_at": datetime.now().isoformat(),
                            "layout_type": target_page.get("layout_type", "standard"),
                            "regenerated": True
                        }
                    }
                else:
                    single_page_result = {"success": False, "error": f"生成的页面数量不足，无法获取第 {page_index + 1} 页"}
            else:
                single_page_result = {"success": False, "error": result.get("error", "重新生成失败")}
        else:
            # 使用保存的设计规范重新生成指定页面（推荐方式）
            logger.info(f"使用保存的设计规范重新生成第 {page_index + 1} 页")
            await async_progress_callback("恢复设计规范", 30.0, "正在恢复原始设计规范...")
            
            try:
                # {{CHENGQI:
                # Action: Removed
                # Timestamp: 2025-01-16 22:33:00 +08:00
                # Reason: 移除重复导入，DesignSpecification已在文件头部导入
                # Principle_Applied: DRY - 避免重复导入
                # Optimization: 减少重复代码，提高代码整洁性
                # Architectural_Note (AR): 统一导入管理
                # Documentation_Note (DW): 移除函数内部的重复导入
                # }}
                # 重新构建设计规范对象
                
                # 恢复设计规范
                design_spec_data = saved_design_spec
                design_spec = DesignSpecification(
                    content_outline=design_spec_data.get('content_outline', []),
                    theme_selection=design_spec_data.get('theme_selection', {}),
                    color_palette=design_spec_data.get('color_palette', {}),
                    layout_principles=design_spec_data.get('layout_principles', {}),
                    typography_system=design_spec_data.get('typography_system', {}),
                    canvas_size=design_spec_data.get('canvas_size', {'width': 1920, 'height': 1080}),
                    created_at=design_spec_data.get('created_at', datetime.now().isoformat()),
                    raw_spec=design_spec_data.get('raw_spec', ''),
                    content_mapping=design_spec_data.get('content_mapping', {})
                )
                
                # 设置SVG生成器的设计规范
                svg_generator.design_spec = design_spec
                svg_generator.source_content = session_data.get("original_content", content)
                
                # 确认设计规范（跳过用户确认步骤）
                svg_generator.current_stage = GenerationStage.CONFIRMED
                
                await async_progress_callback("重新生成页面", 50.0, f"正在重新生成第 {page_index + 1} 页...")
                
                # 使用原有的页面标题，如果没有则使用传入的标题
                original_page_title = page_title
                for page in design_spec.content_outline:
                    if page.get("page_number") == page_index + 1:
                        original_page_title = page.get("title", page_title)
                        break
                
                # 重新生成指定页面
                page_result = await svg_generator.generate_svg_page(
                    page_number=page_index + 1,
                    custom_adjustments=custom_requirements
                )
                
                if page_result.get("success"):
                    single_page_result = {
                        "success": True,
                        "page_data": {
                            "page_number": page_index + 1,
                            "title": original_page_title,
                            "svg_code": page_result.get("svg_code", ""),
                            "content": page_result.get("svg_code", ""),
                            "generated_at": datetime.now().isoformat(),
                            "layout_type": page_result.get("layout_type", "standard"),
                            "regenerated": True
                        }
                    }
                    logger.info(f"使用设计规范成功重新生成第 {page_index + 1} 页")
                else:
                    single_page_result = {"success": False, "error": page_result.get("error", "重新生成失败")}
                    
            except Exception as spec_error:
                logger.error(f"恢复设计规范失败: {spec_error}")
                single_page_result = {"success": False, "error": f"恢复设计规范失败: {str(spec_error)}"}
        
        result = single_page_result
        
        if result.get("success") and result.get("page_data"):
            # 重新生成成功
            page_data = result["page_data"]
            
            # 更新会话状态中的对应页面
            if session_id in active_svg_sessions:
                generated_pages = active_svg_sessions[session_id].get("generated_pages", [])
                
                # 更新或添加页面数据
                updated = False
                for i, existing_page in enumerate(generated_pages):
                    if existing_page.get("page_number") == page_index + 1:
                        generated_pages[i] = page_data
                        updated = True
                        break
                
                if not updated:
                    # 如果页面不存在，添加新页面
                    generated_pages.append(page_data)
                
                active_svg_sessions[session_id].update({
                    "status": "completed",
                    "progress": 100.0,
                    "stage": "重新生成完成",
                    "message": f"第 {page_index + 1} 页重新生成成功",
                    "generated_pages": generated_pages,
                    "completed_at": time.time()
                })
            
            # 发送页面完成事件
            await async_progress_callback("重新生成完成", 100.0, f"第 {page_index + 1} 页重新生成成功", {
                "type": "svg_page_regenerated",
                "page_data": page_data,
                "page_index": page_index
            })
            
            # 发送新页面数据到WebSocket
            try:
                websocket = manager.session_connections.get(session_id)
                if websocket:
                    regenerated_data = {
                        "type": "svg_page_regenerated",
                        "stage": "重新生成完成",
                        "progress": 100.0,
                        "message": f"第 {page_index + 1} 页重新生成成功",
                        "session_id": session_id,
                        "page_index": page_index,
                        "page_data": page_data
                    }
                    await websocket.send_text(json.dumps(regenerated_data))
                    logger.info(f"发送重新生成的页面数据: 第 {page_index + 1} 页")
            except Exception as e:
                logger.warning(f"发送重新生成页面数据失败: {e}")
            
            logger.info(f"单张SVG重新生成任务完成，会话ID: {session_id}, 页面索引: {page_index}")
            
        else:
            # 重新生成失败
            error_msg = result.get("error", "重新生成失败")
            if session_id in active_svg_sessions:
                active_svg_sessions[session_id].update({
                    "status": "failed",
                    "progress": 0.0,
                    "stage": "重新生成失败",
                    "message": error_msg,
                    "error": error_msg,
                    "completed_at": time.time()
                })
            
            await async_progress_callback("重新生成失败", 0.0, error_msg)
            logger.error(f"单张SVG重新生成任务失败，会话ID: {session_id}, 错误: {error_msg}")
            
    except Exception as e:
        logger.error(f"单张SVG重新生成任务异常，会话ID: {session_id}, 错误: {str(e)}")
        
        # 更新会话状态为异常
        if session_id in active_svg_sessions:
            active_svg_sessions[session_id].update({
                "status": "failed",
                "progress": 0.0,
                "stage": "执行异常",
                "message": f"重新生成异常: {str(e)}",
                "error": str(e),
                "completed_at": time.time()
            })
            
            # 发送错误到WebSocket
            try:
                websocket = manager.session_connections.get(session_id)
                if websocket:
                    error_data = {
                        "type": "svg_regeneration_error",
                        "error": str(e),
                        "session_id": session_id,
                        "page_index": page_index,
                        "message": f"第 {page_index + 1} 页重新生成失败: {str(e)}"
                    }
                    await websocket.send_text(json.dumps(error_data))
            except Exception as ws_error:
                logger.error(f"发送WebSocket错误消息失败: {ws_error}") 